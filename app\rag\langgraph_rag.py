"""LangGraph-based RAG system for the trading assistant."""

import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from langgraph.graph import StateGraph, END
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, AIMessage, SystemMessage

from app.services.vector_store import PineconeVectorStore
from app.memory.memory_manager import MemoryManager
from app.rag.prompt_templates import (
    TRADING_ASSISTANT_SYSTEM_PROMPT,
    RETRIEVAL_PROMPT,
    RESPONSE_GENERATION_PROMPT,
    MEMORY_SUMMARIZATION_PROMPT,
    QUERY_CLASSIFICATION_PROMPT
)
from app.models.schemas import QueryRequest, QueryResponse
from config import settings


class RAGState:
    """State object for the RAG workflow."""
    
    def __init__(self):
        self.query: str = ""
        self.session_id: str = ""
        self.user_id: Optional[str] = None
        self.conversation_context: List[Dict[str, Any]] = []
        self.short_term_memory: List[str] = []
        self.long_term_memory: List[str] = []
        self.retrieved_docs: List[Dict[str, Any]] = []
        self.query_classification: Dict[str, Any] = {}
        self.response: str = ""
        self.confidence_score: float = 0.0
        self.sources: List[str] = []


class TradingAssistantRAG:
    """LangGraph-based RAG system for trading assistance."""
    
    def __init__(self):
        # Initialize components
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=settings.google_api_key,
            temperature=0.3
        )
        
        self.vector_store = PineconeVectorStore()
        self.memory_manager = MemoryManager()
        
        # Build the workflow graph
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow for RAG."""
        
        # Define the workflow steps
        def load_context(state: RAGState) -> RAGState:
            """Load conversation context and memory."""
            try:
                # Get conversation context
                context = self.memory_manager.get_conversation_context(state.session_id)
                state.conversation_context = context.messages
                state.short_term_memory = context.short_term_memory
                state.long_term_memory = context.long_term_memory
                
                print(f"Loaded context: {len(state.conversation_context)} messages, "
                      f"{len(state.short_term_memory)} short-term memories, "
                      f"{len(state.long_term_memory)} long-term memories")
                
            except Exception as e:
                print(f"Error loading context: {e}")
            
            return state
        
        def classify_query(state: RAGState) -> RAGState:
            """Classify the user query."""
            try:
                classification_prompt = QUERY_CLASSIFICATION_PROMPT.format(query=state.query)
                
                messages = [
                    SystemMessage(content="You are a query classification system for trading queries."),
                    HumanMessage(content=classification_prompt)
                ]
                
                response = self.llm.invoke(messages)
                
                # Parse classification (simplified)
                state.query_classification = {
                    "classification": response.content,
                    "timestamp": datetime.now()
                }
                
            except Exception as e:
                print(f"Error classifying query: {e}")
                state.query_classification = {"classification": "General Support"}
            
            return state
        
        def retrieve_documents(state: RAGState) -> RAGState:
            """Retrieve relevant documents from vector store."""
            try:
                # Enhanced query for better retrieval
                enhanced_query = state.query
                
                # Add context from short-term memory if available
                if state.short_term_memory:
                    recent_context = " ".join(state.short_term_memory[-3:])
                    enhanced_query = f"{state.query} Context: {recent_context}"
                
                # Retrieve documents
                results = self.vector_store.similarity_search(
                    query=enhanced_query,
                    top_k=5
                )
                
                state.retrieved_docs = results
                state.sources = [doc.get("source", "") for doc in results]
                
                print(f"Retrieved {len(results)} relevant documents")
                
            except Exception as e:
                print(f"Error retrieving documents: {e}")
                state.retrieved_docs = []
            
            return state
        
        def generate_response(state: RAGState) -> RAGState:
            """Generate response using LLM."""
            try:
                # Format retrieved documents
                retrieved_text = ""
                for i, doc in enumerate(state.retrieved_docs, 1):
                    retrieved_text += f"\n--- Document {i} (Score: {doc.get('score', 0):.3f}) ---\n"
                    retrieved_text += doc.get('content', '')
                    retrieved_text += "\n"
                
                # Format conversation context
                context_text = ""
                for msg in state.conversation_context[-5:]:  # Last 5 messages
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    context_text += f"{role}: {content}\n"
                
                # Format memories
                short_term_text = "\n".join(state.short_term_memory[-3:]) if state.short_term_memory else "None"
                long_term_text = "\n".join(state.long_term_memory[-5:]) if state.long_term_memory else "None"
                
                # Generate response
                response_prompt = RESPONSE_GENERATION_PROMPT.format(
                    query=state.query,
                    retrieved_docs=retrieved_text,
                    conversation_context=context_text,
                    short_term_memory=short_term_text,
                    long_term_memory=long_term_text
                )
                
                messages = [
                    SystemMessage(content=TRADING_ASSISTANT_SYSTEM_PROMPT),
                    HumanMessage(content=response_prompt)
                ]
                
                response = self.llm.invoke(messages)
                state.response = response.content
                
                # Calculate confidence score based on retrieval scores
                if state.retrieved_docs:
                    avg_score = sum(doc.get('score', 0) for doc in state.retrieved_docs) / len(state.retrieved_docs)
                    state.confidence_score = min(avg_score * 1.2, 1.0)  # Scale and cap at 1.0
                else:
                    state.confidence_score = 0.5  # Default confidence
                
                print(f"Generated response with confidence: {state.confidence_score:.3f}")
                
            except Exception as e:
                print(f"Error generating response: {e}")
                state.response = "I apologize, but I'm having trouble processing your request right now. Please try again."
                state.confidence_score = 0.0
            
            return state
        
        def update_memory(state: RAGState) -> RAGState:
            """Update conversation memory."""
            try:
                # Update conversation buffer
                new_messages = state.conversation_context + [
                    {"role": "user", "content": state.query, "timestamp": datetime.now().isoformat()},
                    {"role": "assistant", "content": state.response, "timestamp": datetime.now().isoformat()}
                ]
                
                self.memory_manager.store_conversation_buffer(state.session_id, new_messages)
                
                # Store important information in short-term memory
                if state.confidence_score > 0.7:
                    memory_content = f"Q: {state.query} A: {state.response[:200]}..."
                    self.memory_manager.store_short_term_memory(
                        state.session_id, 
                        memory_content, 
                        state.confidence_score
                    )
                
                # Store in long-term memory if highly relevant
                if state.confidence_score > 0.8:
                    self.memory_manager.store_long_term_memory(
                        content=f"High-quality interaction: {state.query} -> {state.response[:150]}...",
                        importance_score=state.confidence_score,
                        session_id=state.session_id,
                        user_id=state.user_id
                    )
                
                print("Updated memory systems")
                
            except Exception as e:
                print(f"Error updating memory: {e}")
            
            return state
        
        # Build the graph
        workflow = StateGraph(RAGState)
        
        # Add nodes
        workflow.add_node("load_context", load_context)
        workflow.add_node("classify_query", classify_query)
        workflow.add_node("retrieve_documents", retrieve_documents)
        workflow.add_node("generate_response", generate_response)
        workflow.add_node("update_memory", update_memory)
        
        # Add edges
        workflow.set_entry_point("load_context")
        workflow.add_edge("load_context", "classify_query")
        workflow.add_edge("classify_query", "retrieve_documents")
        workflow.add_edge("retrieve_documents", "generate_response")
        workflow.add_edge("generate_response", "update_memory")
        workflow.add_edge("update_memory", END)
        
        return workflow.compile()
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        """Process a query through the RAG workflow."""
        try:
            # Initialize state
            state = RAGState()
            state.query = request.query
            state.session_id = request.session_id or str(uuid.uuid4())
            state.user_id = request.user_id
            
            print(f"Processing query: {request.query[:100]}...")
            
            # Run the workflow
            final_state = self.workflow.invoke(state)
            
            # Create response
            response = QueryResponse(
                response=final_state.response,
                session_id=final_state.session_id,
                sources=final_state.sources,
                confidence=final_state.confidence_score
            )
            
            return response
            
        except Exception as e:
            print(f"Error processing query: {e}")
            return QueryResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                session_id=request.session_id or str(uuid.uuid4()),
                sources=[],
                confidence=0.0
            )
