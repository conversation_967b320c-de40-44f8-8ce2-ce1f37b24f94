"""Pydantic models and schemas for the Trading Assistant."""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    """Schema for a chat message."""
    timestamp: datetime
    sender: str
    content: str
    message_type: str = "text"  # text, file, voice, etc.


class QueryRequest(BaseModel):
    """Schema for incoming query requests."""
    query: str = Field(..., description="User's trading query")
    session_id: Optional[str] = Field(None, description="Session ID for conversation tracking")
    user_id: Optional[str] = Field(None, description="User ID for personalization")


class QueryResponse(BaseModel):
    """Schema for query responses."""
    response: str = Field(..., description="Assistant's response")
    session_id: str = Field(..., description="Session ID")
    sources: List[str] = Field(default=[], description="Source documents used")
    confidence: float = Field(default=0.0, description="Confidence score")
    timestamp: datetime = Field(default_factory=datetime.now)


class DocumentChunk(BaseModel):
    """Schema for document chunks."""
    content: str
    metadata: Dict[str, Any]
    chunk_id: str
    source: str
    timestamp: Optional[datetime] = None


class MemoryEntry(BaseModel):
    """Schema for memory entries."""
    content: str
    timestamp: datetime
    memory_type: str  # short_term, long_term, conversation
    importance_score: float = 0.0
    session_id: Optional[str] = None
    user_id: Optional[str] = None


class ConversationContext(BaseModel):
    """Schema for conversation context."""
    session_id: str
    user_id: Optional[str] = None
    messages: List[Dict[str, Any]] = []
    short_term_memory: List[str] = []
    long_term_memory: List[str] = []
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
