"""FastAPI application for the Trading Assistant."""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
import logging
from datetime import datetime

from app.models.schemas import QueryRequest, QueryResponse
from app.rag.langgraph_rag import TradingAssistantRAG
from app.utils.data_processor import WhatsAppDataProcessor
from app.services.vector_store import PineconeVectorStore
from config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global variables for components
rag_system: TradingAssistantRAG = None
vector_store: PineconeVectorStore = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global rag_system, vector_store
    
    logger.info("Starting Trading Assistant application...")
    
    try:
        # Initialize components
        logger.info("Initializing vector store...")
        vector_store = PineconeVectorStore()
        
        logger.info("Initializing RAG system...")
        rag_system = TradingAssistantRAG()
        
        # Check if we need to process and index data
        index_stats = vector_store.get_index_stats()
        if index_stats.get("total_vectors", 0) == 0:
            logger.info("No vectors found in index. Processing and indexing data...")
            await process_and_index_data()
        else:
            logger.info(f"Found {index_stats.get('total_vectors', 0)} vectors in index")
        
        logger.info("Application startup complete!")
        
    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    
    yield
    
    logger.info("Shutting down Trading Assistant application...")


async def process_and_index_data():
    """Process WhatsApp data and index it in the vector store."""
    try:
        logger.info("Processing WhatsApp chat data...")
        
        # Process data
        processor = WhatsAppDataProcessor()
        chunks = processor.process_data()
        
        if chunks:
            logger.info(f"Indexing {len(chunks)} document chunks...")
            success = vector_store.upsert_documents(chunks)
            
            if success:
                logger.info("Data processing and indexing completed successfully!")
            else:
                logger.error("Failed to index documents")
        else:
            logger.warning("No document chunks created from data processing")
            
    except Exception as e:
        logger.error(f"Error processing and indexing data: {e}")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI-powered trading assistant with RAG capabilities for funded trading support",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Trading Assistant API",
        "version": settings.app_version,
        "status": "active",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "chat": "/chat - POST - Main chat endpoint for trading queries",
            "health": "/health - GET - Health check endpoint",
            "stats": "/stats - GET - System statistics"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check vector store connection
        vector_stats = vector_store.get_index_stats() if vector_store else {}
        
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "rag_system": "active" if rag_system else "inactive",
                "vector_store": "active" if vector_store else "inactive",
                "vector_count": vector_stats.get("total_vectors", 0)
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.get("/stats")
async def get_stats():
    """Get system statistics."""
    try:
        stats = {
            "timestamp": datetime.now().isoformat(),
            "vector_store": vector_store.get_index_stats() if vector_store else {},
            "application": {
                "name": settings.app_name,
                "version": settings.app_version,
                "debug": settings.debug
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving statistics")


@app.post("/chat", response_model=QueryResponse)
async def chat_endpoint(request: QueryRequest, background_tasks: BackgroundTasks):
    """
    Main chat endpoint for trading queries.
    
    This endpoint processes trading-related queries using RAG (Retrieval Augmented Generation)
    with conversation memory and returns helpful responses.
    """
    try:
        logger.info(f"Received query: {request.query[:100]}...")
        
        if not rag_system:
            raise HTTPException(status_code=503, detail="RAG system not initialized")
        
        # Validate request
        if not request.query or len(request.query.strip()) == 0:
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        if len(request.query) > 2000:
            raise HTTPException(status_code=400, detail="Query too long (max 2000 characters)")
        
        # Process query
        response = await rag_system.process_query(request)
        
        logger.info(f"Generated response with confidence: {response.confidence:.3f}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/reindex")
async def reindex_data(background_tasks: BackgroundTasks):
    """
    Reindex the data in the vector store.
    This endpoint is useful for updating the knowledge base.
    """
    try:
        if not vector_store:
            raise HTTPException(status_code=503, detail="Vector store not initialized")
        
        # Add reindexing task to background
        background_tasks.add_task(process_and_index_data)
        
        return {
            "message": "Reindexing started in background",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error starting reindex: {e}")
        raise HTTPException(status_code=500, detail="Error starting reindex process")


# Exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.core.app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
