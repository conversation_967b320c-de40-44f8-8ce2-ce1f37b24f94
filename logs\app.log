2025-07-19 17:43:56,294 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:43:56,296 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:43:56,309 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:43:56,310 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:44:12,558 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-07-19 17:44:39,743 - app.core.app - ERROR - Error during startup: init is no longer a top-level attribute of the pinecone package.

Please create an instance of the Pinecone class instead.

Example:

    import os
    from pinecone import Pinecone, ServerlessSpec

    pc = Pinecone(
        api_key=os.environ.get("PINECONE_API_KEY")
    )

    # Now do stuff
    if 'my_index' not in pc.list_indexes().names():
        pc.create_index(
            name='my_index',
            dimension=1536,
            metric='euclidean',
            spec=ServerlessSpec(
                cloud='aws',
                region='us-west-2'
            )
        )


2025-07-19 17:46:37,739 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:46:37,740 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:46:37,746 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:46:37,747 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:46:50,999 - app.core.app - ERROR - Error during startup: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'content-type': 'text/plain; charset=utf-8', 'access-control-allow-origin': '*', 'vary': 'origin,access-control-request-method,access-control-request-headers', 'access-control-expose-headers': '*', 'x-pinecone-api-version': '2025-04', 'x-cloud-trace-context': '197d3b1330963e5608475d00f0ef56da', 'date': 'Sat, 19 Jul 2025 12:46:50 GMT', 'server': 'Google Frontend', 'Content-Length': '200', 'Via': '1.1 google', 'Alt-Svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
HTTP response body: {"error":{"code":"INVALID_ARGUMENT","message":"Bad request: Your free plan does not support indexes in the us-west-2 region of aws. To create indexes in this region, upgrade your plan."},"status":400}

2025-07-19 17:51:18,970 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:51:18,970 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:51:18,976 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:51:18,976 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:51:32,972 - app.core.app - ERROR - Error during startup: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'content-type': 'text/plain; charset=utf-8', 'access-control-allow-origin': '*', 'vary': 'origin,access-control-request-method,access-control-request-headers', 'access-control-expose-headers': '*', 'x-pinecone-api-version': '2025-04', 'x-cloud-trace-context': '032f5fa6c0aa5de4eda70b47870c1719', 'date': 'Sat, 19 Jul 2025 12:51:32 GMT', 'server': 'Google Frontend', 'Content-Length': '202', 'Via': '1.1 google', 'Alt-Svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
HTTP response body: {"error":{"code":"INVALID_ARGUMENT","message":"Bad request: Your free plan does not support indexes in the us-central1 region of gcp. To create indexes in this region, upgrade your plan."},"status":400}

2025-07-19 17:57:25,151 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:57:25,153 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:57:25,172 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:57:25,184 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:57:43,078 - app.core.app - ERROR - Error during startup: (404)
Reason: Not Found
HTTP response headers: HTTPHeaderDict({'content-type': 'text/plain; charset=utf-8', 'access-control-allow-origin': '*', 'vary': 'origin,access-control-request-method,access-control-request-headers', 'access-control-expose-headers': '*', 'x-pinecone-api-version': '2025-04', 'x-cloud-trace-context': '1c95885f8da29efe70bb9004569ac7b8', 'date': 'Sat, 19 Jul 2025 12:57:43 GMT', 'server': 'Google Frontend', 'Content-Length': '102', 'Via': '1.1 google', 'Alt-Svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
HTTP response body: {"error":{"code":"NOT_FOUND","message":"Resource cloud: gcp region: us-east1 not found"},"status":404}

2025-07-19 18:12:38,211 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 18:12:38,211 - app.core.app - INFO - Initializing vector store...
2025-07-19 18:12:38,220 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 18:12:38,220 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 18:13:15,704 - app.core.app - INFO - Initializing RAG system...
2025-07-19 18:13:16,141 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 18:13:16,141 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 18:13:31,525 - app.core.app - INFO - No vectors found in index. Processing and indexing data...
2025-07-19 18:13:31,527 - app.core.app - INFO - Processing WhatsApp chat data...
2025-07-19 18:13:31,836 - app.core.app - WARNING - No document chunks created from data processing
2025-07-19 18:13:31,838 - app.core.app - INFO - Application startup complete!
2025-07-19 18:20:24,169 - app.core.app - INFO - Received query: waht is prop firm...
2025-07-19 18:20:24,364 - app.core.app - INFO - Generated response with confidence: 0.000
2025-07-19 18:33:19,792 - app.core.app - INFO - Shutting down Trading Assistant application...
2025-07-19 18:33:42,881 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 18:33:42,881 - app.core.app - INFO - Initializing vector store...
2025-07-19 18:33:42,884 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 18:33:42,884 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 18:33:57,478 - app.core.app - INFO - Initializing RAG system...
2025-07-19 18:33:57,587 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 18:33:57,588 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 18:34:14,193 - app.core.app - INFO - No vectors found in index. Processing and indexing data...
2025-07-19 18:34:14,193 - app.core.app - INFO - Processing WhatsApp chat data...
2025-07-19 18:34:14,368 - app.core.app - WARNING - No document chunks created from data processing
2025-07-19 18:34:14,370 - app.core.app - INFO - Application startup complete!
2025-07-19 18:37:03,350 - app.core.app - INFO - Started new conversation: 2516e1f2-14fc-4d13-bbef-1dedbb5b1978
2025-07-19 18:37:39,120 - app.core.app - INFO - Received query in conversation 2516e1f2-14fc-4d13-bbef-1dedbb5b1978: i need help about how prop firm work...
2025-07-19 18:37:48,757 - app.core.app - INFO - Generated response for conversation 2516e1f2-14fc-4d13-bbef-1dedbb5b1978 with confidence: 0.500
2025-07-19 18:38:29,673 - app.core.app - INFO - Received query in conversation 2516e1f2-14fc-4d13-bbef-1dedbb5b1978: what are the rule for passing hft challenge...
2025-07-19 18:38:36,560 - app.core.app - INFO - Generated response for conversation 2516e1f2-14fc-4d13-bbef-1dedbb5b1978 with confidence: 0.500
