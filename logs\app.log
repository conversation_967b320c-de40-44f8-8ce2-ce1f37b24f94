2025-07-19 17:43:56,294 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:43:56,296 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:43:56,309 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:43:56,310 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:44:12,558 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-07-19 17:44:39,743 - app.core.app - ERROR - Error during startup: init is no longer a top-level attribute of the pinecone package.

Please create an instance of the Pinecone class instead.

Example:

    import os
    from pinecone import Pinecone, ServerlessSpec

    pc = Pinecone(
        api_key=os.environ.get("PINECONE_API_KEY")
    )

    # Now do stuff
    if 'my_index' not in pc.list_indexes().names():
        pc.create_index(
            name='my_index',
            dimension=1536,
            metric='euclidean',
            spec=ServerlessSpec(
                cloud='aws',
                region='us-west-2'
            )
        )


2025-07-19 17:46:37,739 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:46:37,740 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:46:37,746 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:46:37,747 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:46:50,999 - app.core.app - ERROR - Error during startup: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'content-type': 'text/plain; charset=utf-8', 'access-control-allow-origin': '*', 'vary': 'origin,access-control-request-method,access-control-request-headers', 'access-control-expose-headers': '*', 'x-pinecone-api-version': '2025-04', 'x-cloud-trace-context': '197d3b1330963e5608475d00f0ef56da', 'date': 'Sat, 19 Jul 2025 12:46:50 GMT', 'server': 'Google Frontend', 'Content-Length': '200', 'Via': '1.1 google', 'Alt-Svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
HTTP response body: {"error":{"code":"INVALID_ARGUMENT","message":"Bad request: Your free plan does not support indexes in the us-west-2 region of aws. To create indexes in this region, upgrade your plan."},"status":400}

2025-07-19 17:51:18,970 - app.core.app - INFO - Starting Trading Assistant application...
2025-07-19 17:51:18,970 - app.core.app - INFO - Initializing vector store...
2025-07-19 17:51:18,976 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-19 17:51:18,976 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-19 17:51:32,972 - app.core.app - ERROR - Error during startup: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'content-type': 'text/plain; charset=utf-8', 'access-control-allow-origin': '*', 'vary': 'origin,access-control-request-method,access-control-request-headers', 'access-control-expose-headers': '*', 'x-pinecone-api-version': '2025-04', 'x-cloud-trace-context': '032f5fa6c0aa5de4eda70b47870c1719', 'date': 'Sat, 19 Jul 2025 12:51:32 GMT', 'server': 'Google Frontend', 'Content-Length': '202', 'Via': '1.1 google', 'Alt-Svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
HTTP response body: {"error":{"code":"INVALID_ARGUMENT","message":"Bad request: Your free plan does not support indexes in the us-central1 region of gcp. To create indexes in this region, upgrade your plan."},"status":400}

